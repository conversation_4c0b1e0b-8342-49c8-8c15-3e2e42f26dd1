import { JRequest } from '@/services/index'
import { UserType } from '@/enum/userTypeEnum'

/** 签名发送 */
const enum AccountApiEnum {
  login = '/applet/login',
  jtlogin = '/applet/jtLogin',
  getStateInfo = '/applet/redirect',
  getPhone = '/applet/getAppletPhone',
}

export interface LoginResp {
  idNo: string
  mobile: string
  nickname: string
  token: string
  name: string
  gender: string
  type: UserType
}

/**
 * @description 登录
 */
export async function accountLogin(params: { code: string }) {
  let _url = `${AccountApiEnum.login}?code=${params.code}`
  return JRequest.post<LoginResp>({
    url: _url,
    requestConfig: {
      withToken: true,
    },
  })
}

export async function getStateInfo(state: string) {
  return JRequest.get<{
    productId: string
    hasMobile: boolean
  }>({
    url: `${AccountApiEnum.getStateInfo}?key=${state}`,
  })
}

/**
 * @description 获取手机号
 */
export async function getUserPhoneByCode(phoneCode: string) {
  return JRequest.post<string>({
    url: `${AccountApiEnum.getPhone}?phoneCode=${phoneCode}`,
  })
}
