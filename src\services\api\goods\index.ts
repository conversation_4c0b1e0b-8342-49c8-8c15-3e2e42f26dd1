import { JRequest } from '@/services/index'

/** 商品Api */
const enum ProductApiEnum {
  detail = '/applet/product/manage/detail',
  category = '/applet/product/cate/list',
  goodsList = '/applet/product/manage/page',
  pageLogin = '/applet/product/manage/pageLogin',
  pageSearchProduct = '/applet/product/entity/pageSearchProduct',
  dosageForm = '/product/manage/get/dosageForm',
}

/**
 * @description 获取商品分类
 */
export async function getProductCategoryList(_params) {
  return JRequest.post({
    url: ProductApiEnum.category,
    params: _params,
  })
}

/**
 * @description 获取商品列表
 */
export async function getGoodsList(_params) {
  return JRequest.post({
    url: ProductApiEnum.goodsList,
    params: _params,
  })
}

/**
 * @description 获取商品列表
 */
export async function getGoodslistSearch(_params) {
  return JRequest.post({
    url: ProductApiEnum.pageSearchProduct,
    params: _params,
  })
}

/**
 * @description 获取商品详情
 */
export async function getProductDetailsById(id: string) {
  return JRequest.get({
    url: `${ProductApiEnum.detail}?id=${id}`,
  })
}

/**
 * @description 获取药品剂型
 */
export async function getDosageForm(params = {}) {
  return JRequest.post({
    url: ProductApiEnum.dosageForm,
    params,
  })
}
