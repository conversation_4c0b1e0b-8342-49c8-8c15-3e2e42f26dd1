/**
 * 注意配置: withToken：是否需要携带token（true：不携带， false：携带）
 */
import { JRequest } from '@/services/index'

/** 积分商品 */
const enum PointProductApiEnum {
  get = '/applet/pointProduct/get',
  page = '/applet/pointProduct/page/cache',
  search = '/applet/pointProduct/page/search',
  getPointSift = '/applet/pointSift/list', // 获取积分商品筛选条件
}

/**
 * @description 根据积分商品ID查询详情
 */
export async function getPointProductDetail(pointProductId: string) {
  return JRequest.get({
    url: `${PointProductApiEnum.get}?pointProductId=${pointProductId}`,
  })
}

/**
 * @description 分页查询积分商品(无搜索条件 商品走缓存)
 */
export async function getPointProductPage(params = {}) {
  return JRequest.post({
    url: PointProductApiEnum.page,
    params,
  })
}

/**
 * @description 分页查询积分商品(搜索 商品不走缓存)
 */
export async function getPointProductSearch(params = {}) {
  return JRequest.post({
    url: PointProductApiEnum.search,
    params,
  })
}

/**
 * @description 获取积分商品筛选条件
 */
export async function getPointSift() {
  return JRequest.post({
    url: PointProductApiEnum.getPointSift,
    requestConfig: {
      withToken: false,
    },
  })
}
