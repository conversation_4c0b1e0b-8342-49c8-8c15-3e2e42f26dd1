import { JRequest } from '@/services/index'

/** 购物车Api */
const enum CartApiEnum {
  add = '/applet/cartItem/add',
  update = '/applet/cartItem/update',
  updateNumber = '/applet/cartItem/updateNumber',
  delete = '/applet/cartItem/delete',
  deleteAll = '/applet/cartItem/delete/all',
  page = '/applet/cartItem/page',
  countPrice = '/applet/cartItem/countPrice',
  countCartNumber = '/applet/cartItem/countCartNumber',
  preOrderItem = '/applet/cartItem/add/preOrderItem',
}

/**
 * @description 查询用户购物车
 */
export async function getUserCartCount() {
  return JRequest.get({
    url: CartApiEnum.countCartNumber,
  })
}

/**
 * @description 新增临时购物项
在商品详情点击立即购买，会在缓存生成一个临时的购物项
前端接收到响应之后，再去调 非处方药订单确认 接口，或者 去开处方 的接口
 */
export async function addPreOrderItem(_params: {
  productId: string
  isPres: 0 | 1
  count: number
  specId: string
}) {
  return JRequest.post({
    url: CartApiEnum.preOrderItem,
    params: {
      data: _params,
    },
  })
}

/**
 * @description 统计购物车勾选价格
 */
export async function cartCountPrice(
  _params: Array<{
    count: number
    specId: string
  }>,
) {
  return JRequest.post({
    url: CartApiEnum.countPrice,
    params: {
      data: _params,
    },
  })
}

/**
 * @description 查询购物车列表
 */
export async function getCartPage(_params) {
  return JRequest.post({
    url: CartApiEnum.page,
    params: _params,
  })
}
