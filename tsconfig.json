{"compilerOptions": {"composite": true, "skipLibCheck": true, "module": "ESNext", "moduleResolution": "Node", "resolveJsonModule": true, "noImplicitThis": true, "allowSyntheticDefaultImports": true, "allowJs": true, "sourceMap": true, "baseUrl": ".", "paths": {"@/*": ["./src/*"], "@vant/weapp/*": ["/wxcomponents/vant/*"]}, "outDir": "dist", "lib": ["esnext", "dom"], "types": ["@dcloudio/types", "@types/wechat-miniprogram"]}, "vueCompilerOptions": {"target": 3, "nativeTags": ["block", "template", "component", "slot"]}, "exclude": ["node_modules"], "include": ["src/**/*.ts", "src/**/*.js", "src/**/*.d.ts", "src/**/*.tsx", "src/**/*.jsx", "src/**/*.vue", "src/**/*.json"]}