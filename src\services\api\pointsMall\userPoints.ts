/**
 * 注意配置: withToken：是否需要携带token（true：不携带， false：携带）
 */
import { JRequest } from '@/services/index'

/** 用户积分Api */
const enum IntegralStoreHome {
  getRule = '/applet/pointRule/list',
  getMemberLevel = '/applet/PointLevel/getCsLevel',
  findRemindExpirePoints = '/applet/pointRecord/findRemindExpirePoints',
  getAvailPoints = '/applet/pointRecord/getAvailPoints',
  getPointsRecord = '/applet/pointRecord/pointsRecord',
  getPointChannel = '/applet/pointChannel/list',
}

/**
 * @description 积分商城首页-积分余额
 */
export async function getAvailPoints(channelId: string) {
  return JRequest.get({
    url: `${IntegralStoreHome.getAvailPoints}?channelId=${channelId}`,
    requestConfig: {
      withToken: false,
    },
  })
}

/**
 * @description 积分商城首页-获取积分渠道列表（只获取当前用户参与的）
 */
export async function getPointChannel() {
  return JRequest.post({
    url: IntegralStoreHome.getPointChannel,
    requestConfig: {
      withToken: false,
    },
  })
}

/** 积分过期提醒 */
export async function findRemindExpirePoints() {
  return JRequest.post({
    url: IntegralStoreHome.findRemindExpirePoints,
    requestConfig: {
      withToken: false,
    },
  })
}

/**
 * @description 积分商城首页-积分规则
 */
export async function getPointsRule() {
  return JRequest.post({
    url: IntegralStoreHome.getRule,
    requestConfig: {
      withToken: false,
    },
  })
}

/**
 * @description 积分商城首页-查询当前会员等级
 */
export async function getMemberLevel() {
  return JRequest.get({
    url: IntegralStoreHome.getMemberLevel,
  })
}
