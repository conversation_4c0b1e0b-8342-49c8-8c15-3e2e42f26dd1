<template>
  <view>
    <view style="display: flex; align-items: center; gap: 4rpx" @click="show = true">
      <span class="search-type-name">{{ searchTypeText }}</span>
      <img :src="searchUpIcon" alt="" class="search-type-icon" :class="{ 'rotate-180': show }" />
    </view>

    <van-action-sheet
      v-model:show="show"
      cancel-text="取消"
      close-on-click-action
      :actions="actions"
      @select="onSelect"
      @cancel="show = false"
      @click-overlay="show = false"
    />
  </view>
</template>

<script lang="ts" setup>
import { ref, computed } from 'vue'
import { StoreOrderTypeEnum } from '@/enum'
import searchUpIcon from '@/static/images/storeHome/searchUpIcon.png'

defineOptions({ name: 'StoreSelectOrderType' })

const props = defineProps<{ value: StoreOrderTypeEnum }>()
const emit = defineEmits<{ (e: 'update:value', value: StoreOrderTypeEnum): void }>()

const show = ref(false)

const searchTypeOptions = [
  { text: '普通订单', value: StoreOrderTypeEnum.NORMAL },
  { text: '福利券订单', value: StoreOrderTypeEnum.COUPON },
  { text: '积分订单', value: StoreOrderTypeEnum.INTEGRAL },
]

const actions = computed(() =>
  searchTypeOptions.map((item) => ({ name: item.text, value: item.value })),
)

const searchTypeText = computed(() => {
  const option = searchTypeOptions.find((item) => item.value === props.value)
  return option ? option.text : ''
})

function onSelect(action: { detail: { name: string; value: StoreOrderTypeEnum } }) {
  console.log(action)

  emit('update:value', action.detail.value)
  show.value = false
}
</script>

<style lang="scss" scoped>
.search-type-name {
  font-family: Source Han Sans CN, Source Han Sans CN;
  font-weight: 400;
  font-size: 28rpx;
  color: #666666;
  line-height: 40rpx;
  text-align: right;
  font-style: normal;
  text-transform: none;
}
.search-type-icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: 8rpx;
  transform: rotateX(180deg);
  transition: transform 0.3s ease;
}
.rotate-180 {
  transform: rotateX(0deg) !important;
}
</style>
