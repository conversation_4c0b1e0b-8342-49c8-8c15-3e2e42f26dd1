/**
 * 注意配置: withToken：是否需要携带token（true：不携带， false：携带）
 */
import { JRequest } from '@/services/index'
import { SystemParamKeyEnum, SystemPDFKeyEnum } from '@/enum'

/** 系统参数配置接口 */
const enum SystemApiEnum {
  // 获取系统全局参数配置
  getGlobalConfigs = '/applet/globalConfigs/getGlobalConfigs',
  // 获取协议
  getAgreementByKey = '/applet/globalConfigs/getAgreement',
}

/**
 * @description 获取全局配置
 */
export async function getGlobalConfigs(params: { version: string }) {
  let _url = `${SystemApiEnum.getGlobalConfigs}?currentVersion=${params.version}`
  return JRequest.get({
    url: _url,
    requestConfig: {
      withToken: false,
    },
  })
}

/**
 * @description 获取协议
 */
export async function getAgreementByKey(params: SystemParamKeyEnum | SystemPDFKeyEnum) {
  let _url = `${SystemApiEnum.getAgreementByKey}?key=${params}`
  return JRequest.get({
    url: _url,
    requestConfig: {
      withToken: true,
    },
  })
}
