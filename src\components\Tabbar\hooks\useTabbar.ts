import { ref } from 'vue'
import { RoutesName } from '@/routes/enums/routeNameEnum'
import { routesMap } from '@/routes/maps'
import { listNavigationConfig } from '@/services/api'
import { useSystemStoreWithoutSetup } from '@/stores/modules/system'

const navigationPageEnum = {
  /**首页 */
  0: 'home',
  /**分类 */
  1: 'cate',
  /**个人中心页 */
  2: 'user',
}

const navigationMap = {
  [navigationPageEnum[0]]: {
    pagePath: routesMap[RoutesName.StoreHome].path,
    text: '首页',
    iconPath: '/static/images/storeTabbar/home.png',
    selectedIconPath: '/static/images/storeTabbar/home-active.png',
  },
  [navigationPageEnum[1]]: {
    pagePath: routesMap[RoutesName.StoreCategory].path,
    text: '分类',
    iconPath: '/static/images/storeTabbar/cate.png',
    selectedIconPath: '/static/images/storeTabbar/cate-active.png',
  },
  [navigationPageEnum[2]]: {
    pagePath: routesMap[RoutesName.StoreMine].path,
    text: '我的',
    iconPath: '/static/images/storeTabbar/mine.png',
    selectedIconPath: '/static/images/storeTabbar/mine-active.png',
  },
}

const defaultNavigation = [
  navigationMap[navigationPageEnum[0]],
  navigationMap[navigationPageEnum[1]],
  navigationMap[navigationPageEnum[2]],
]

/** 当前Tab */
const selectedTabKeyRef = ref(routesMap[RoutesName.StoreHome].path)
/** 导航list */
const navigationListRef = ref([...defaultNavigation])
/** 是否隐藏tabbar */
const isHiddenTabbar = ref(false)

export function useTabbar() {
  const systemStore = useSystemStoreWithoutSetup()
  async function loadNavigationConfig() {
    if (!navigationListRef.value.length) {
      let navigationList = []
      try {
        const resp = await listNavigationConfig()
        if (!resp.length) {
          navigationList = defaultNavigation
        } else {
          navigationList = resp.map((item) => {
            return {
              ...navigationMap[navigationPageEnum[item.pointPage]],
              text: item.name,
              selectedIconPath: item.iconCheckedPath,
              iconPath: item.iconUncheckedPath,
            }
          })
        }
      } catch (e) {
        console.log(e)
        navigationList = defaultNavigation
      }
      try {
        systemStore.setNavigationConfigList(navigationList)
        navigationListRef.value = navigationList
        if (navigationListRef.value[0].pagePath !== 'pages/Home/index') {
          wx.switchTab({ url: `${navigationListRef.value[0].pagePath}` })
        }
      } catch (e) {
        console.log(e)
      }
    }
  }
  function setSelectedTabKey(key: string) {
    selectedTabKeyRef.value = key
  }
  function setTabbarDisplay(status: boolean) {
    isHiddenTabbar.value = !status
  }

  return {
    loadNavigationConfig,
    selectedTabKeyRef,
    navigationListRef,
    setSelectedTabKey,
    isHiddenTabbar,
    setTabbarDisplay,
  }
}
