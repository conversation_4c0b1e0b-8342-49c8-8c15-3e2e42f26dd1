/**
 * 注意配置: withToken：是否需要携带token（true：不携带， false：携带）
 */
import { JRequest } from '@/services/index'

/** 首页Api */
const enum HomeApiEnum {
  swiperList = '/applet/carouselImg/listImg',
  headerInfo = '/applet/homeLogo/getLogo',
}

/**
 * @description 获取轮播图
 * @param {number} CurrentPosition 位置
 */
export async function getSwiperList(params: { currentPosition: number }) {
  return JRequest.get({
    url: `${HomeApiEnum.swiperList}?CurrentPosition=${params.currentPosition}`,
    requestConfig: {
      withToken: true,
    },
  })
}

/**
 * @description 获取首页logo信息
 */
export async function getHomeLogoInfo() {
  return JRequest.get({
    url: HomeApiEnum.headerInfo,
    requestConfig: {
      withToken: true,
    },
  })
}
