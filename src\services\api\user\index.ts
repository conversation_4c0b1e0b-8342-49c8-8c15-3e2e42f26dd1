import { JRequest } from '@/services/index'

/** 用户信息接口API */
const enum UserApiEnum {
  getUserInfo = '/applet/customerEntity/getCustomerInfo', //获取用户信息
  editUserInfo = '/applet/customerEntity/editCsInfo', //修改用户信息
  authentication = '/applet/customerEntity/updateCsAuth', //身份认证
  updateAvatar = '/common/upload', //上传文件
  getPres = '/applet/pres/getPres', //获取待使用处方
  getCountMyOrders = '/applet/order/countMyOrders', //统计我的订单数量
}

/**
 * @description 获取用户信息
 */
export async function getUserInfo() {
  return JRequest.get({
    url: UserApiEnum.getUserInfo,
    requestConfig: {
      withToken: false,
    },
  })
}

/**
 * @description 获取待使用处方
 */
export async function getUserPres() {
  return JRequest.post({
    url: UserApiEnum.getPres,
    requestConfig: {
      withToken: false,
    },
  })
}

/**
 * @description 统计我的订单数量
 */
export async function getUserOrderCount() {
  return JRequest.get({
    url: UserApiEnum.getCountMyOrders,
    requestConfig: {
      withToken: false,
    },
  })
}
