import { ref, reactive, watch, effectScope, onScopeDispose } from 'vue'
import { OrderStatusEnum } from '@/enum'
import { useMessages } from '@/hooks/common'
import { getMyOrderList, getMyAfterSaleOrderList } from '@/services/api'

export default function useGetMineOrder(_params: { orderType: OrderStatusEnum }) {
  const scope = effectScope()
  const { createMessageSuccess, createMessageError } = useMessages()
  const isPageLoadingRef = ref(false)

  /** tab */
  const activeTabRef = ref(Number(_params.orderType))

  /** 我的订单状态 */
  const orderStatusList = [
    {
      label: '全部',
      value: OrderStatusEnum.ALL,
    },
    {
      label: '待付款',
      value: OrderStatusEnum.WAIT_PAY,
    },
    {
      label: '待发货/提货',
      value: OrderStatusEnum.WAIT_DELIVER,
    },
    {
      label: '待收货',
      value: OrderStatusEnum.WAIT_RECEIVE,
    },
    {
      label: '退款/售后',
      value: OrderStatusEnum.REFUND,
    },
  ]

  /** 我的订单数据 */
  const storeMineOrderList = ref([
    {
      id: '1956286342594170882',
      createTime: '2025-08-15 17:26:25',
      updateTime: '2025-08-15 17:48:33',
      code: '1956286341494870018',
      type: 1,
      attribute: 10,
      pickupType: 1,
      verificationType: 0,
      presStatus: 0,
      money: 1,
      goodsAmount: 1,
      payedMoney: 1,
      status: 5,
      completeType: 1,
      fromType: 7,
      wxPayNo: '4200002778202508150416329111',
      customerNickname: '陌生人',
      orderItemDTOList: [
        {
          id: '1956286342644633601',
          createTime: '2025-08-15 17:26:25',
          updateTime: '2025-08-15 17:26:35',
          customerId: '1954139380146765825',
          orderId: '1956286342594170882',
          productId: '1955557877741916161',
          productImgPath:
            'https://sg-uat1-oss.jiuwei.cloud/store/image/20250813/ac782982cc02a6460f5446f087ff2a7e_mthumb.png?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=Srq6AdOOzr%2F20250821%2Fus-east-1%2Fs3%2Faws4_request&X-Amz-Date=20250821T030014Z&X-Amz-Expires=604800&X-Amz-SignedHeaders=host&X-Amz-Signature=99c935271c14959b9594ca376de462229af0b12295dc4cf89ca0c73fadf8e252',
          productName: '测试',
          productFrontName: '测试',
          specId: '1955557877834059778',
          specName: '222',
          spu: '11',
          supplierId: '1955541932747001858',
          type: 3,
          isPres: 0,
          medicineType: 0,
          isVirtual: 0,
          priceType: 0,
          price: 1,
          count: 1,
          upper: 999,
          payType: 1,
          onlinePayment: 1,
          cashOnDelivery: 0,
          isCashOnDelivery: 0,
          isDownPayment: 0,
          isPoint: 0,
          returnPoints: 11,
          isUseCashCoupon: 0,
          isAllocation: 0,
          isDistribution: 0,
          dealerCommission: 11,
          version: '1956286263284076545',
        },
      ],
      isShippingInfoPushWx: false,
    },
    {
      id: '1956268651921408001',
      createTime: '2025-08-15 16:16:07',
      updateTime: '2025-08-18 15:00:03',
      code: '1956268650629824513',
      type: 1,
      attribute: 10,
      pickupType: 1,
      verificationType: 0,
      presStatus: 0,
      money: 1,
      goodsAmount: 1,
      payedMoney: 1,
      status: 4,
      completeType: 1,
      fromType: 7,
      wxPayNo: '4200002776202508151778717835',
      customerNickname: '陌生人',
      orderItemDTOList: [
        {
          id: '1956268652236111874',
          createTime: '2025-08-15 16:16:07',
          updateTime: '2025-08-15 16:16:19',
          customerId: '1954139380146765825',
          orderId: '1956268651921408001',
          productId: '1955557877741916161',
          productImgPath:
            'https://sg-uat1-oss.jiuwei.cloud/store/image/20250813/ac782982cc02a6460f5446f087ff2a7e_mthumb.png?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=Srq6AdOOzr%2F20250821%2Fus-east-1%2Fs3%2Faws4_request&X-Amz-Date=20250821T030014Z&X-Amz-Expires=604800&X-Amz-SignedHeaders=host&X-Amz-Signature=99c935271c14959b9594ca376de462229af0b12295dc4cf89ca0c73fadf8e252',
          productName: '测试',
          productFrontName: '测试',
          specId: '1955557877834059778',
          specName: '222',
          spu: '11',
          supplierId: '1955541932747001858',
          type: 3,
          isPres: 0,
          medicineType: 0,
          isVirtual: 0,
          priceType: 0,
          price: 1,
          count: 1,
          upper: 999,
          payType: 1,
          onlinePayment: 1,
          cashOnDelivery: 0,
          isCashOnDelivery: 0,
          isDownPayment: 0,
          isPoint: 0,
          returnPoints: 11,
          isUseCashCoupon: 0,
          isAllocation: 0,
          isDistribution: 0,
          dealerCommission: 11,
          version: '1956268614412017665',
        },
      ],
      isShippingInfoPushWx: false,
    },
    {
      id: '1956267303499210753',
      createTime: '2025-08-15 16:10:45',
      updateTime: '2025-08-18 15:00:03',
      code: '1956267300885766145',
      type: 1,
      attribute: 10,
      pickupType: 1,
      verificationType: 0,
      presStatus: 0,
      money: 10,
      goodsAmount: 10,
      payedMoney: 10,
      status: 4,
      completeType: 1,
      fromType: 7,
      wxPayNo: '4200002725202508152440607696',
      customerNickname: '陌生人',
      orderItemDTOList: [
        {
          id: '1956267303658070018',
          createTime: '2025-08-15 16:10:46',
          updateTime: '2025-08-15 16:11:30',
          customerId: '1954139380146765825',
          orderId: '1956267303499210753',
          productId: '1955207180223381505',
          productImgPath:
            'https://sg-uat1-oss.jiuwei.cloud/store/image/20250812/359c003ec3f6bfd373dd3637d47fe522_mthumb.png?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=Srq6AdOOzr%2F20250819%2Fus-east-1%2Fs3%2Faws4_request&X-Amz-Date=20250819T054137Z&X-Amz-Expires=604800&X-Amz-SignedHeaders=host&X-Amz-Signature=da8499f47d32014b844d582732a395f8ab7407cf4446c5401a606eb54de96e3a',
          productName: '测试商品-普通商品001',
          productFrontName: '测试商品-普通商品001',
          specId: '1955207180357468162',
          specName: '1',
          spu: '',
          type: 3,
          isPres: 0,
          medicineType: 0,
          isVirtual: 0,
          priceType: 0,
          price: 10,
          count: 1,
          upper: 999,
          payType: 1,
          onlinePayment: 10,
          cashOnDelivery: 0,
          isCashOnDelivery: 0,
          isDownPayment: 0,
          isPoint: 0,
          isUseCashCoupon: 0,
          isAllocation: 0,
          isDistribution: 0,
          version: '1956249785493622786',
        },
      ],
      isShippingInfoPushWx: false,
    },
    {
      id: '1956259442094112769',
      createTime: '2025-08-15 15:39:31',
      updateTime: '2025-08-15 15:41:48',
      code: '1956259441921884162',
      type: 1,
      attribute: 10,
      pickupType: 2,
      verificationType: 1,
      presStatus: 0,
      money: 1,
      goodsAmount: 1,
      payedMoney: 1,
      status: 5,
      completeType: 0,
      fromType: 7,
      wxPayNo: '4200002723202508150058431436',
      customerNickname: '陌生人',
      orderItemDTOList: [
        {
          id: '1956259442111021058',
          createTime: '2025-08-15 15:39:31',
          updateTime: '2025-08-15 15:39:40',
          customerId: '1954139380146765825',
          orderId: '1956259442094112769',
          productId: '1955816255487348737',
          productImgPath:
            'https://sg-uat1-oss.jiuwei.cloud/store/image/20250814/e2cffdd781ed23585d6f802e66748d0c_mthumb.jpg?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=Srq6AdOOzr%2F20250820%2Fus-east-1%2Fs3%2Faws4_request&X-Amz-Date=20250820T095454Z&X-Amz-Expires=604800&X-Amz-SignedHeaders=host&X-Amz-Signature=45abb02cb638565e340075b18e5a29ed88d7432fd96f7bf263c8ec56e3adddd0',
          productName: '铅笔',
          productFrontName: '铅笔',
          specId: '1955816255986339841',
          specName: '2',
          spu: '111',
          supplierId: '1955541932747001858',
          type: 3,
          isPres: 0,
          medicineType: 0,
          isVirtual: 0,
          priceType: 1,
          price: 100,
          marketPrice: 100,
          activityPrice: 1,
          count: 1,
          upper: 999,
          payType: 1,
          onlinePayment: 1,
          cashOnDelivery: 0,
          isCashOnDelivery: 0,
          isDownPayment: 0,
          isPoint: 0,
          returnPoints: 100,
          isUseCashCoupon: 0,
          isAllocation: 0,
          isDistribution: 0,
          dealerCommission: 1,
          costPrice: 100,
          version: '1955821324945326082',
        },
      ],
      isShippingInfoPushWx: false,
      orderVerificationDTO: {
        id: '1956259482313035777',
        code: '1956259482304516097',
        path: '1956259441921884162',
        orderId: '1956259442094112769',
        orderCode: '1956259441921884162',
      },
    },
  ])
  /** 是否加载完 */
  const isFinishedRef = ref(false)
  /** 刷新 */
  const refreshingRef = ref(false)
  /** 加载 */
  const isLoadingRef = ref(false)
  /** 分页 */
  const pageVO = reactive({
    size: 100,
    current: 1,
    total: 0,
  })

  /** 加载数据 */
  function onLoadData() {
    if (pageVO.current * pageVO.size < pageVO.total) {
      isLoadingRef.value = true
      pageVO.current++
      if (activeTabRef.value === OrderStatusEnum.REFUND) {
        getStoreMineAfterSaleOrderList()
      } else {
        getStoreMineOrderList()
      }
    }
  }

  /** 获取搜索参数 */
  function getSearchParams() {
    const status = [OrderStatusEnum.ALL, OrderStatusEnum.REFUND].includes(activeTabRef.value)
      ? null
      : activeTabRef.value
    return {
      data: {
        status,
      },
      pageVO: {
        current: pageVO.current,
        size: pageVO.size,
      },
    }
  }

  /** 获取我的订单（不包含售后订单） */
  async function getStoreMineOrderList() {
    const { current, size } = pageVO

    try {
      isPageLoadingRef.value = current === 1
      const _params = getSearchParams()
      const { records = [], total = 0 } = await getMyOrderList(_params)

      // 更新订单列表
      if (current === 1) {
        storeMineOrderList.value = records
      } else if (records.length) {
        storeMineOrderList.value.push(...records)
      }

      // 更新分页状态
      const hasMore = current * size < total
      Object.assign(pageVO, {
        current: current,
        total: Number(total),
      })
      isFinishedRef.value = !hasMore
    } catch (error) {
      createMessageError('加载失败，请稍后重试')
      ininParams()
    } finally {
      isLoadingRef.value = false
      refreshingRef.value = false
      isPageLoadingRef.value = false
    }
  }

  /** 获取售后订单列表数据 */
  async function getStoreMineAfterSaleOrderList() {
    const { current, size } = pageVO

    try {
      isPageLoadingRef.value = current === 1
      const _params = getSearchParams()
      const { records = [], total = 0 } = await getMyAfterSaleOrderList(_params)

      // 更新订单列表
      if (current === 1) {
        storeMineOrderList.value = records
      } else if (records.length) {
        storeMineOrderList.value.push(...records)
      }

      // 更新分页状态
      const hasMore = current * size < total
      Object.assign(pageVO, {
        current: current,
        total: Number(total),
      })
      isFinishedRef.value = !hasMore
    } catch (error) {
      createMessageError('加载失败，请稍后重试')
      ininParams()
    } finally {
      isLoadingRef.value = false
      refreshingRef.value = false
      isPageLoadingRef.value = false
    }
  }

  /** tabs 切换回调 */
  function onTabsChange(e) {
    activeTabRef.value = e?.detail?.name
  }

  function ininParams() {
    pageVO.current = 1
    pageVO.total = 0
    isFinishedRef.value = false
  }

  /** 刷新 */
  async function onRefresh() {
    ininParams()
    // 重新加载数据
    refreshingRef.value = true
    if (
      [
        OrderStatusEnum.ALL,
        OrderStatusEnum.WAIT_PAY,
        OrderStatusEnum.WAIT_DELIVER,
        OrderStatusEnum.WAIT_RECEIVE,
      ].includes(activeTabRef.value)
    ) {
      await initStoreMineOrderList()
    } else if (activeTabRef.value == OrderStatusEnum.REFUND) {
      /** 退款/售后另外接口 */
      await initStoreMineOrderRefundList()
    }
    //结束下拉刷新
    uni.stopPullDownRefresh()
  }

  /** 数据初始化 */
  async function initStoreMineOrderList() {
    isPageLoadingRef.value = true
    ininParams()
    await getStoreMineOrderList()
    isPageLoadingRef.value = false
  }

  /** 售后订单数据初始化 */
  async function initStoreMineOrderRefundList() {
    isPageLoadingRef.value = true
    ininParams()
    await getStoreMineAfterSaleOrderList()
    isPageLoadingRef.value = false
  }

  /** 在作用域内运行监听器 */
  scope.run(() => {
    /** 监听 */
    watch(
      () => activeTabRef.value,
      (newVal) => {
        if (
          [
            OrderStatusEnum.ALL,
            OrderStatusEnum.WAIT_PAY,
            OrderStatusEnum.WAIT_DELIVER,
            OrderStatusEnum.WAIT_RECEIVE,
          ].includes(newVal)
        ) {
          initStoreMineOrderList()
        } else if (newVal == OrderStatusEnum.REFUND) {
          /** 退款/售后另外接口 */
          initStoreMineOrderRefundList()
        }
      },
    )
  })

  /** 作用域销毁时清理 */
  onScopeDispose(() => {
    scope.stop()
  })

  return {
    activeTabRef,
    orderStatusList,
    isPageLoadingRef,
    storeMineOrderList,
    isFinishedRef,
    refreshingRef,
    isLoadingRef,
    onTabsChange,
    onLoadData,
    onRefresh,
    initStoreMineOrderList,
    initStoreMineOrderRefundList,
  }
}
