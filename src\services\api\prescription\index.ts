import { JRequest } from '@/services/index'

/** 处方Api */
const enum PrescriptionApi {
  myPage = '/applet/pres/page',
  preDetail = '/applet/pres/getDetail',
  deletePre = '/applet/pres/delete',
  add = '/applet/pres/add',
  check = '/applet/pres/checkAndFindProductPresOptions',
  viewCecord = '/applet/pres/getRecord',
  toEdit = '/applet/pres/toEdit',
}

/**
 * @description 
 *  1、校验商品是否处方药：
       若是药品商品（处方药）在申请：获取商品相关病症；
       若是疗法商品在申请：则不返回病症信息；
    2、跳转到处方编辑页面
 */
export async function toEditPres(_params) {
  return JRequest.post({
    url: PrescriptionApi.toEdit,
    params: {
      data: _params,
    },
  })
}
