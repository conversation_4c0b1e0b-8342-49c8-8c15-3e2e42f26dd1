import { RoutesName } from '@/routes/enums/routeNameEnum'
import type { RoutesMap } from '@/routes/types'

export const StoreHome: RoutesMap = {
  [RoutesName.StoreHome]: {
    path: 'pages/home/<USER>',
    style: {
      navigationBarTitleText: '首页',
      enablePullDownRefresh: true,
      navigationBarBackgroundColor: '#fff',
    },
  },
  [RoutesName.StoreLogin]: {
    path: 'pages/login/index',
    style: {
      navigationBarTitleText: '',
      enablePullDownRefresh: false,
      navigationStyle: 'custom',
    },
  },
}
