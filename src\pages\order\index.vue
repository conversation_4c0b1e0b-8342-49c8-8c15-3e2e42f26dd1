<template>
  <view class="store_mine_order_wrapper">
    <!-- tabs -->
    <!-- #ifdef MP-WEIXIN -->
    <van-tabs
      :style="{ '--tabs-line-height': '80rpx' }"
      :active="activeTabRef"
      color="#EF1115"
      title-active-color="#EF1115"
      title-inactive-color="#333333"
      line-width="56rpx"
      line-height="4rpx"
      swipeable
      :duration="0.3"
      @change="onTabsChange"
    >
      <van-tab
        v-for="item in orderStatusList"
        :key="item.value"
        :name="item.value"
        :title="item.label"
        title-style="font-family: Source Han Sans CN, Source Han Sans CN;font-weight: 400;font-size: 28rpx;"
      >
        <scroll-view
          scroll-y
          enhanced
          :show-scrollbar="false"
          refresher-enabled
          :refresher-triggered="refreshingRef"
          @scrolltolower="onLoadData"
          @refresherrefresh="onRefresh"
          class="tab-content"
        >
          <template v-if="storeMineOrderList.length">
            <template v-if="[OrderStatusEnum.REFUND].includes(activeTabRef)">
              <StoreAfterSalesOrderCard
                v-for="item in storeMineOrderList"
                :key="item.id"
                :orderInfo="item"
                @click=""
              />
            </template>
            <template v-else>
              <StoreMineOrderCard
                v-for="item in storeMineOrderList"
                :key="item.id"
                :orderInfo="item"
                @writeOffCode=""
                @cancelOrder=""
                @click=""
                @confirmReceipt=""
                @viewLogistics=""
              />
            </template>
            <view class="no-data">没有更多数据了</view>
          </template>
          <!-- 数据为空 -->
          <template v-else>
            <JEmptyData style="min-height: 400px" />
          </template>
        </scroll-view>
      </van-tab>
    </van-tabs>
    <!-- #endif -->
    <JTabs
      :style="{ '--van-tabs-line-height': '80rpx', height: '100%' }"
      v-model:active="activeTabRef"
      color="#EF1115"
      title-active-color="#EF1115"
      title-inactive-color="#333333"
      line-width="72rpx"
      line-height="4rpx"
      swipeable
    >
      <JTab v-for="item in orderStatusList" :key="item.value" :name="item.value">
        <template #title>
          <text class="j-tab-title" :class="{ 'j-tab-active': activeTabRef === item.value }">
            {{ item.label }}
          </text>
        </template>
        <JPullRefresh
          v-model="refreshingRef"
          @refresh="onRefresh"
          class="j-tab-content"
          :class="`j-tab-content_${item.value}`"
          @scroll=""
        >
          <template v-if="storeMineOrderList.length">
            <JList
              v-model:loading="isLoadingRef"
              :finished="isFinishedRef"
              finished-text="没有更多了"
              @load="onLoad"
            >
              <template v-if="[OrderStatusEnum.REFUND].includes(activeTabRef)">
                <StoreAfterSalesOrderCard
                  v-for="item in storeMineOrderList"
                  :key="item.id"
                  :orderInfo="item"
                  @click=""
                />
              </template>
              <template v-else>
                <StoreMineOrderCard
                  v-for="item in storeMineOrderList"
                  :key="item.id"
                  :orderInfo="item"
                  @writeOffCode=""
                  @cancelOrder=""
                  @click=""
                  @confirmReceipt=""
                  @viewLogistics=""
                />
              </template>
            </JList>
          </template>
          <template v-else>
            <JEmptyData />
          </template>
        </JPullRefresh>
      </JTab>
    </JTabs>
  </view>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { onLoad, onPullDownRefresh } from '@dcloudio/uni-app'
import { OrderStatusEnum } from '@/enum'
import { useGetMineOrder } from './hooks'
/** 相关组件 */
import JEmptyData from '@/components/JEmptyData/index.vue'
import JLoadingWrapper from '@/components/JLoadingWrapper/index.vue'
import StoreMineOrderCard from './components/StoreMineOrderCard.vue'
import StoreAfterSalesOrderCard from './components/StoreAfterSalesOrderCard.vue'

defineOptions({ name: 'Order' })

const {
  orderStatusList,
  activeTabRef,
  storeMineOrderList,
  initStoreMineOrderList,
  initStoreMineOrderRefundList,
  onLoadData,
  onTabsChange,
  onRefresh,
  isFinishedRef,
  refreshingRef,
  isPageLoadingRef,
  isLoadingRef,
} = useGetMineOrder({ orderType: OrderStatusEnum.ALL })

onLoad((options) => {
  const v = Number(options?.status)
  if (!Number.isNaN(v)) {
    activeTabRef.value = v as OrderStatusEnum
  }
})
</script>

<style lang="scss" scoped>
.store_mine_order_wrapper {
  width: 100%;
  height: 100%;
  /*  #ifdef MP-WEIXIN */
  :deep(.van-tab) {
    height: 80rpx;
    flex: auto;
  }
  :deep(.van-tabs__line) {
    bottom: 8rpx;
  }
  /*  #endif  */

  /*  #ifdef H5 */
  :deep(.van-tab) {
    flex: auto;
  }
  :deep(.van-tabs__line) {
    bottom: 32rpx;
  }
  :deep(.van-tabs) {
    .van-tabs__wrap {
      height: 80rpx;
    }

    .van-tabs__content {
      height: calc(100% - 80rpx);
      .van-tab__panel {
        height: 100%;
      }
    }
  }
  /*  #endif  */
  .j-tab-title,
  .j-tab-active {
    font-family:
      Source Han Sans CN,
      Source Han Sans CN;
    font-weight: 400;
    font-size: 28rpx;
    text-align: center;
    font-style: normal;
    text-transform: none;
  }
  .j-tab-title {
    color: #666666;
  }
  .j-tab-active {
    color: #ef1115;
  }
  /*  #ifdef MP-WEIXIN */
  .tab-content {
    height: calc(100vh - 80rpx);
    background: #f8f8f8;
    padding: 0rpx 24rpx;
    padding-bottom: env(safe-area-inset-bottom);
    box-sizing: border-box;
    .no-data {
      font-family:
        Source Han Sans CN,
        Source Han Sans CN;
      font-weight: 400;
      font-size: 28rpx;
      color: #999999;
      line-height: 40rpx;
      text-align: center;
      font-style: normal;
      text-transform: none;
      margin: 24rpx 0;
    }
  }
  /*  #endif  */
  /*  #ifdef H5 */
  .j-tab-content {
    height: 100%;
    background: #f8f8f8;
    padding: 24rpx 20rpx;
    box-sizing: border-box;
    overflow-y: auto;
  }
  /*  #endif  */
}
</style>
