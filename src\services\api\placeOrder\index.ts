import { JRequest } from '@/services/index'

/** 下订单 */
const enum PlaceOrderApi {
  bindGroupManager = '/applet/placeOrder/bindGroupManager',
  pageSearchSgGmMember = '/applet/placeOrder/pageSearchSgGmMember',
  pageSearchSgGmCourse = '/applet/placeOrder/pageSearchSgGmCourse',
  pagePurchaseAgentPres = '/applet/placeOrder/pres/page',
  nonPresConfirmOrder = '/applet/placeOrder/order/confirmOrder/nonPres',
  presConfirmOrder = '/applet/placeOrder/order/confirmOrder/pres',
  createOrder = '/applet/placeOrder/order/createOrder',
  orderByStatus = '/applet/placeOrder/order/queryOrderByStatus',
  orderDetail = '/applet/placeOrder/order/getOrderDetail',
  confirmCustomer = '/applet/placeOrder/confirmCustomer',
  shareOrder = '/applet/placeOrder/order/shareOrderByGm',
  redirectToOrderDetail = '/applet/redirectToOrderDetail',
  presAdd = '/applet/placeOrder/pres/add',
}

/**
 * @description 绑定群管
 */
export async function bindGroupManager(key) {
  return JRequest.get({
    url: `${PlaceOrderApi.bindGroupManager}?key=${key}`,
  })
}
