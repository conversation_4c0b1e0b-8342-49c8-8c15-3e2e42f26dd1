import { defineConfig, loadEnv } from 'vite'
import uni from '@dcloudio/vite-plugin-uni'
// @see https://unocss.dev/
import UnoCSS from 'unocss/vite'

// https://vitejs.dev/config/
export default ({ command, mode }) => {
  const { UNI_PLATFORM } = process.env
  const env = loadEnv(mode, process.cwd())
  console.log('UNI_PLATFORM -> ', UNI_PLATFORM) // 得到 mp-weixin, h5, app 等

  return defineConfig({
    plugins: [uni(), UnoCSS()],
    server: {
      port: Number(env.VITE_DEV_PORT),
      host: '127.0.0.1',
    },
  })
}
