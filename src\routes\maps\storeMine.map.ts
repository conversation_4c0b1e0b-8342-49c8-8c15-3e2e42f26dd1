import { RoutesName } from '@/routes/enums/routeNameEnum'
import type { RoutesMap } from '@/routes/types'

export const StoreMine: RoutesMap = {
  [RoutesName.StoreMine]: {
    path: 'pages/mine/index',
    style: {
      navigationBarTitleText: '个人中心页',
      enablePullDownRefresh: false,
      navigationBarBackgroundColor: '#fff',
    },
  },
  [RoutesName.StoreMyOrders]: {
    path: 'pages/order/index',
    style: {
      navigationBarTitleText: '我的订单',
      enablePullDownRefresh: false,
      navigationBarBackgroundColor: '#fff',
    },
  },
}
