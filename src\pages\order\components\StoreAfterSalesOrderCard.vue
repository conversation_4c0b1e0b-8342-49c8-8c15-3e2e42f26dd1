<template>
  <view class="wrapper">
    <view class="after-orders-header">
      <view class="order-code">
        <text class="order-code-text">{{ `售后单号：${orderInfoRef?.recordNo}` }}</text>
        <img
          :src="CopySrc"
          alt=""
          @click.stop="handleCopyID(orderInfoRef?.recordNo)"
          class="order-code-img"
        />
      </view>
      <!-- 状态 -->
      <text class="after-orders-status">{{ afterSaleStatusMap[orderInfoRef?.state] }}</text>
    </view>
    <!-- 订单信息 -->
    <view class="after-orders-info">
      <img
        :src="firstOrderItem?.productImgPath ? firstOrderItem?.productImgPath : CouponSrc"
        alt=""
        class="after-orders-info-img"
      />
      <view class="after-orders-info-text">
        <view class="after-orders-info-title">
          <p class="van-multi-ellipsis--l2 commodity-title">
            {{ firstOrderItem?.productFrontName }}
          </p>
          <view class="after-orders-info-right">
            <text class="price">
              {{ `¥ ${Number((firstOrderItem?.price ?? 0) / 100).toFixed(2)}` }}
            </text>
            <!-- 数量 -->
            <text class="count">{{ `x ${firstOrderItem?.count}` }}</text>
          </view>
        </view>
        <!-- 规格 -->
        <view class="specification">{{ firstOrderItem?.specName }}</view>
        <!-- 订单金额 -->
        <view class="order-amount">
          <text>{{ afterSaleTypeMap[orderInfoRef?.type] }}</text>
          <text class="prefix">￥</text>
          <text class="refund-amount">
            {{ `${Number((orderInfoRef?.refundAmount ?? 0) / 100).toFixed(2)}` }}
          </text>
        </view>
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { toRefs, computed } from 'vue'
import { copyText } from '@/utils/clipboardUtils'
import { StoreAfterSaleTypeEnum, AfterSaleStatusEnum } from '@/enum'
import { useMessages } from '@/hooks/common'
/** 静态资源 */
import CopySrc from '@/static/images/storeUser/copy-icon.png'
import CouponSrc from '@/static/images/storeHome/coupon.png'

defineOptions({ name: 'StoreAfterSalesOrderCard' })

/** props */
const props = defineProps<{
  orderInfo: {
    type?: StoreAfterSaleTypeEnum
    recordNo?: string
    state?: number // 售后状态
    refundAmount?: number // 退款金额
    orderItemDTOList?: Array<{
      type?: 1 | 2 | 3
      orderId?: string
      productImgPath?: string
      productFrontName?: string
      specName?: string
      price?: number
      count?: number
      exchangePoints?: number
      exchangePrice?: number
    }>
  }
}>()

/** emit */
const emit = defineEmits<{
  /** 核销码 */
  (e: 'writeOffCode', orderCode: string): void
  /** 取消订单 */
  (e: 'cancelOrder'): void
  /** 付款 */
  (e: 'paymentOrder'): void
}>()

const { orderInfo: orderInfoRef } = toRefs(props)
const { createMessageSuccess, createMessageError } = useMessages()

/** 获取第一项订单项 */
const firstOrderItem = computed(() => {
  return orderInfoRef.value?.orderItemDTOList?.[0]
})

/** 售后类型 */
const afterSaleTypeMap = {
  [StoreAfterSaleTypeEnum.REFUND_RETURN]: '退货退款，退款金额',
  [StoreAfterSaleTypeEnum.REFUND]: '退款金额',
  [StoreAfterSaleTypeEnum.CANCEL_ORDER]: '仅取消订单',
}

/** 售后状态 */
const afterSaleStatusMap = {
  [AfterSaleStatusEnum.NON_AFTER_SALE]: '非售后状态',
  [AfterSaleStatusEnum.PENDING_MERCHANT]: '待商家受理',
  [AfterSaleStatusEnum.CUSTOMER_WITHDRAWN]: '已取消',
  [AfterSaleStatusEnum.MERCHANT_REFUSED]: '退款失败',
  [AfterSaleStatusEnum.PENDING_RETURN]: '待您退货',
  [AfterSaleStatusEnum.PENDING_MERCHANT_RECEIPT]: '待商家收货',
  [AfterSaleStatusEnum.RETURN_CLOSED]: '已关闭',
  [AfterSaleStatusEnum.REFUNDING]: '退款中',
  [AfterSaleStatusEnum.PENDING_PAYMENT]: '退款中',
  [AfterSaleStatusEnum.REFUND_COMPLETED]: '退款成功',
  [AfterSaleStatusEnum.CANCEL_APPROVED]: '商家同意取消订单',
  [AfterSaleStatusEnum.CANCEL_REJECTED]: '商家拒绝取消订单',
}

/** 复制订单ID */
function handleCopyID(data) {
  try {
    copyText(data)
    createMessageSuccess('复制售后单号成功')
  } catch (e) {
    createMessageError('复制售后单号失败')
  }
}
</script>

<style lang="scss" scoped>
.wrapper {
  width: 100%;
  background: #ffffff;
  border-radius: 16rpx;
  padding: 24rpx;
  box-sizing: border-box;
  margin-top: 16rpx;
  display: flex;
  flex-direction: column;
  gap: 16rpx;

  .after-orders-header {
    display: flex;
    align-items: center;
    gap: 8rpx;

    .order-code {
      display: flex;
      align-items: center;

      .order-code-text {
        font-family:
          Source Han Sans CN,
          Source Han Sans CN;
        font-weight: 500;
        font-size: 24rpx;
        color: #333333;
        text-align: left;
        font-style: normal;
        text-transform: none;
      }

      .order-code-img {
        width: 32rpx;
        height: 32rpx;
        margin-left: 8rpx;
      }
    }

    .after-orders-status {
      font-family:
        Source Han Sans CN,
        Source Han Sans CN;
      font-weight: 400;
      font-size: 28rpx;
      color: #ff6864;
      text-align: right;
      font-style: normal;
      text-transform: none;
      margin-left: auto;
    }
  }

  .after-orders-info {
    display: flex;
    gap: 16rpx;

    .after-orders-info-img {
      width: 128rpx;
      height: 128rpx;
      border-radius: 16rpx;
    }

    .after-orders-info-text {
      flex: 1;
      display: flex;
      flex-direction: column;
      gap: 16rpx;

      .after-orders-info-title {
        display: flex;
        gap: 8rpx;

        .commodity-title {
          flex: 1;
          font-family:
            Source Han Sans CN,
            Source Han Sans CN;
          font-weight: 500;
          font-size: 32rpx;
          color: #333333;
          text-align: left;
          font-style: normal;
          text-transform: none;
          line-height: 22px;
        }

        .after-orders-info-right {
          min-width: 176rpx;
          display: flex;
          flex-direction: column;
          gap: 16rpx;

          .price {
            font-family:
              Source Han Sans CN,
              Source Han Sans CN;
            font-weight: 600;
            font-size: 30rpx;
            color: #333333;
            text-align: right;
            font-style: normal;
            text-transform: none;
          }

          .count {
            font-family:
              Source Han Sans CN,
              Source Han Sans CN;
            font-weight: 400;
            font-size: 26rpx;
            color: #333333;
            text-align: right;
            font-style: normal;
            text-transform: none;
          }
        }
      }

      .specification {
        font-family:
          Source Han Sans CN,
          Source Han Sans CN;
        font-weight: 400;
        font-size: 28rpx;
        color: #666666;
        text-align: left;
        font-style: normal;
        text-transform: none;
      }

      .order-amount {
        font-family:
          Source Han Sans CN,
          Source Han Sans CN;
        font-weight: 500;
        font-size: 30rpx;
        color: #333333;
        text-align: right;
        font-style: normal;
        text-transform: none;
        .prefix {
          font-size: 28rpx;
        }
        .refund-amount {
          font-size: 32rpx;
        }
      }
    }
  }
}
</style>
